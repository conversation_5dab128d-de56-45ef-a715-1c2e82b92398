=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 7 boîtes détectées
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: arrow, confiance: 0.84
  [OK] Flèche acceptée avec confiance 0.84 >= 0.4
  Détection: arrow, confiance: 0.81
  [OK] Flèche acceptée avec confiance 0.81 >= 0.4
  Détection: arrow, confiance: 0.63
  [OK] Flèche acceptée avec confiance 0.63 >= 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 6
  Classe détectée: generalization
  Classe détectée: generalization
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 6 boîtes détectées
  Box 0: coords=605,468,709,605 class_idx=2 conf=0.69
  Détection: generalization, confiance: 0.69
  Box 1: coords=1918,527,2034,680 class_idx=2 conf=0.67
  Détection: generalization, confiance: 0.67
  Box 2: coords=1032,329,1095,391 class_idx=3 conf=0.48
  Détection: endpoin, confiance: 0.48
  Box 3: coords=1674,288,1736,344 class_idx=3 conf=0.43
  Détection: endpoin, confiance: 0.43
  Box 4: coords=1911,928,1984,998 class_idx=3 conf=0.42
  Détection: endpoin, confiance: 0.42
  Box 5: coords=597,972,700,1074 class_idx=3 conf=0.39
  Détection: endpoin, confiance: 0.39

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 4, 'arrow': 3}
  Modèle 2: {'generalization': 2, 'endpoin': 4}

=== FIN DÉTECTION ===
