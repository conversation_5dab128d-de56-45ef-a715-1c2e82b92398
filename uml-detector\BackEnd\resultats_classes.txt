class 1:
NOM_CLASSE: Implementon
ATTRIBUTS: 
MÉTHODES:

class 2:
NOM_CLASSE: Concrete Implementor
ATTRIBUTS: 
MÉTHODES:

class 3:
NOM_CLASSE: Abstraction
ATTRIBUTS: fnc
MÉTHODES:

class 4:
NOM_CLASSE: Refine
ATTRIBUTS: ref
MÉTHODES:



----- R<PERSON>SUMÉ DES RELATIONS -----
• 2 relation(s) de type generalization
• 1 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Concrete Implementor hérite de Implementon (héritage (généralisation))
• Refine hérite de Abstraction (héritage (généralisation))
• Il y a une relation de association entre Abstraction et Implementon